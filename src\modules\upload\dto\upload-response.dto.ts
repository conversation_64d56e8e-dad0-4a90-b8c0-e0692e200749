import { ApiProperty } from '@nestjs/swagger';

export class UploadUrlResponseDto {
  @ApiProperty({
    description: 'Presigned URL for uploading the file',
    example:
      'https://your-account-id.r2.cloudflarestorage.com/bucket/path/file.jpg?X-Amz-Algorithm=...',
  })
  uploadUrl: string;

  @ApiProperty({
    description: 'File key in the storage',
    example:
      'banner/2024/01/507f1f77bcf86cd799439011/1704067200000_a1b2c3d4.jpg',
  })
  fileKey: string;

  @ApiProperty({
    description: 'Public URL to access the file after upload',
    example:
      'https://your-custom-domain.com/banner/2024/01/507f1f77bcf86cd799439011/1704067200000_a1b2c3d4.jpg',
  })
  publicUrl: string;

  @ApiProperty({
    description: 'URL expiration time in seconds',
    example: 900,
  })
  expiresIn: number;

  @ApiProperty({
    description: 'Upload instructions',
    example: 'Use PUT method to upload the file to the uploadUrl',
  })
  instructions: string;
}

export class DeleteFileResponseDto {
  @ApiProperty({
    description: 'Success message',
    example: 'File deleted successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Deleted file key',
    example:
      'banner/2024/01/507f1f77bcf86cd799439011/1704067200000_a1b2c3d4.jpg',
  })
  fileKey: string;
}

export class UploadErrorResponseDto {
  @ApiProperty({
    description: 'Error message',
    example: 'File type not allowed for this category',
  })
  message: string;

  @ApiProperty({
    description: 'Error code',
    example: 'INVALID_FILE_TYPE',
  })
  error: string;

  @ApiProperty({
    description: 'HTTP status code',
    example: 400,
  })
  statusCode: number;
}
