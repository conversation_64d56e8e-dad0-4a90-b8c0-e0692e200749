import { Modu<PERSON> } from '@nestjs/common';
import { BookingAgentController } from './controllers/booking-agent.controller';
import { BookingPageAgentController } from './controllers/booking-page-agent.controller';
import { UploadAgentController } from './controllers/upload-agent.controller';
import { BookingModule } from '../booking/booking.module';
import { BookingPageModule } from '../booking-page/booking-page.module';
import { UploadModule } from '../upload/upload.module';
import { CommonModule } from 'src/common/common.module';

@Module({
  imports: [BookingModule, BookingPageModule, UploadModule, CommonModule],
  controllers: [
    BookingAgentController,
    BookingPageAgentController,
    UploadAgentController,
  ],
})
export class AgentModule {}
