MONGO_URI='****************************************************************'
PORT=1001

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PREFIX=booking-easy
REDIS_URI=redis://localhost:6379

# JWT
JWT_SECRET=secret
# 7D
JWT_EXPIRATION_TIME=604800
# 30D
JWT_REFRESH_TOKEN_EXPIRATION_TIME=2592000

# Email
EMAIL_MODE="resend"
GOOGLE_CLIENT_ID=

MAILERSEND_API_KEY=
MAILERSEND_FROM_EMAIL=

RESEND_API_KEY=
RESEND_FROM_EMAIL="BookOne <<EMAIL>>"

# App
FRONTEND_URL=http://localhost:3000

# Security
# To enable login restriction, add allowed emails through the API
# POST /auth/allowed-emails with { "email": "<EMAIL>" }
# If no emails are added to the allowed list, all emails can login

# R2 Storage (Cloudflare R2)
R2_ENDPOINT=https://a4ebf9115d09e6ea09fbc108b938f572.r2.cloudflarestorage.com/
R2_ACCESS_KEY_ID=your-r2-access-key-id
R2_SECRET_ACCESS_KEY=your-r2-secret-access-key
R2_BUCKET_NAME=booking-easy-uploads
R2_PUBLIC_URL=https://your-custom-domain.com