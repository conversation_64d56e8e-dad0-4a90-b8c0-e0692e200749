import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsOptional,
  IsN<PERSON>ber,
  Min,
  Max,
  Matches,
} from 'class-validator';

export enum FileCategory {
  BANNER = 'banner',
  AVATAR = 'avatar',
  DOCUMENT = 'document',
}

export class GenerateUploadUrlDto {
  @ApiProperty({
    description: 'Original filename',
    example: 'banner-image.jpg',
  })
  @IsString()
  originalName: string;

  @ApiProperty({
    description: 'MIME type of the file',
    example: 'image/jpeg',
  })
  @IsString()
  @Matches(/^[a-zA-Z0-9]+\/[a-zA-Z0-9\-\+\.]+$/, {
    message: 'Invalid MIME type format',
  })
  mimeType: string;

  @ApiProperty({
    description: 'File category',
    enum: FileCategory,
    example: FileCategory.BANNER,
  })
  @IsEnum(FileCategory)
  category: FileCategory;

  @ApiProperty({
    description: 'File size in bytes',
    example: 1024000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10 * 1024 * 1024) // 10MB max
  size?: number;

  @ApiProperty({
    description: 'Entity ID (e.g., booking page ID, user ID)',
    example: '507f1f77bcf86cd799439011',
    required: false,
  })
  @IsOptional()
  @IsString()
  entityId?: string;
}

export class DeleteFileDto {
  @ApiProperty({
    description: 'File key or public URL to delete',
    example:
      'banner/2024/01/507f1f77bcf86cd799439011/1704067200000_a1b2c3d4.jpg',
  })
  @IsString()
  fileKey: string;
}
