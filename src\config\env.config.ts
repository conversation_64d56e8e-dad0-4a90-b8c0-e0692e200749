import { Paths } from 'src/common/interfaces/utils';

const envConfig = () =>
  ({
    mode: process.env.NODE_ENV || 'development',

    jwt: {
      secret: process.env.JWT_SECRET,
      expirationTime: parseInt(process.env.JWT_EXPIRATION_TIME),
      refreshTokenExpirationTime: parseInt(
        process.env.JWT_REFRESH_TOKEN_EXPIRATION_TIME,
      ),
    },
    database: {
      uri: process.env.MONGO_URI,
    },
    redis: {
      uri: process.env.REDIS_URI,
    },
    google: {
      clientId: process.env.MONGO_URI,
    },
    email: {
      provider: process.env.EMAIL_PROVIDER || 'resend',
      mailersendApiKey: process.env.MAILERSEND_API_KEY,
      mailersendFromEmail: process.env.MAILERSEND_FROM_EMAIL,
      resendApiKey: process.env.RESEND_API_KEY,
      resendFromEmail: process.env.RESEND_FROM_EMAIL,
      adminName: process.env.ADMIN_NAME ?? 'Admin',
    },
    app: {
      frontendUrl: process.env.FRONTEND_URL,
    },
    r2: {
      endpoint: process.env.R2_ENDPOINT,
      accessKeyId: process.env.R2_ACCESS_KEY_ID,
      secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
      bucketName: process.env.R2_BUCKET_NAME,
      publicUrl: process.env.R2_PUBLIC_URL,
    },
  }) as const;

export type IEnvConfig = ReturnType<typeof envConfig>;

export type EnvConfigType = Record<Paths<IEnvConfig>, any>;

export default envConfig;
