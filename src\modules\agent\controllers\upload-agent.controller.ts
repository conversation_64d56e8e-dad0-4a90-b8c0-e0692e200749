import {
  Controller,
  Post,
  Body,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { UploadService } from 'src/modules/upload/services/upload.service';
import {
  GenerateUploadUrlDto,
  FileCategory,
} from 'src/modules/upload/dto/upload-request.dto';
import {
  UploadUrlResponseDto,
  UploadErrorResponseDto,
} from 'src/modules/upload/dto/upload-response.dto';
import { AuthGuard } from 'src/common/guards/auth.guard';

@ApiTags('agent/upload')
@Controller('agent/upload')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class UploadAgentController {
  constructor(private readonly uploadService: UploadService) {}

  @Post('generate-url')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Generate presigned URL for file upload',
    description:
      'Generate a presigned URL for uploading files to R2 storage. The URL expires in 15 minutes.',
  })
  @ApiBody({ type: GenerateUploadUrlDto })
  @ApiResponse({
    status: 200,
    description: 'Presigned URL generated successfully',
    type: UploadUrlResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid file or validation failed',
    type: UploadErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - JWT token required',
  })
  async generateUploadUrl(@Body() dto: GenerateUploadUrlDto) {
    const data = await this.uploadService.generateUploadUrl(dto);
    return {
      data,
    };
  }

  @Post('banner')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Generate presigned URL for banner upload',
    description:
      'Convenience endpoint for generating presigned URL specifically for banner images',
  })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['originalName', 'mimeType', 'entityId'],
      properties: {
        originalName: {
          type: 'string',
          example: 'banner-image.jpg',
          description: 'Original filename',
        },
        mimeType: {
          type: 'string',
          example: 'image/jpeg',
          description: 'MIME type of the file',
        },
        size: {
          type: 'number',
          example: 1024000,
          description: 'File size in bytes (optional)',
        },
        entityId: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
          description: 'Booking page ID',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Banner upload URL generated successfully',
    type: UploadUrlResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid banner file or validation failed',
    type: UploadErrorResponseDto,
  })
  async generateBannerUploadUrl(
    @Body() body: Omit<GenerateUploadUrlDto, 'category'>,
  ): Promise<UploadUrlResponseDto> {
    const dto: GenerateUploadUrlDto = {
      ...body,
      category: FileCategory.BANNER,
    };
    return this.uploadService.generateUploadUrl(dto);
  }
}
