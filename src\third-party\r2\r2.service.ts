import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { S3Client, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { PutObjectCommand } from '@aws-sdk/client-s3';
import { EnvConfigType } from 'src/config/env.config';
import * as crypto from 'crypto';

export interface PresignedUploadResponse {
  uploadUrl: string;
  fileKey: string;
  publicUrl: string;
  expiresIn: number;
}

export interface FileMetadata {
  originalName: string;
  mimeType: string;
  size?: number;
  category: 'banner' | 'avatar' | 'document';
  entityId?: string; // booking page id, user id, etc.
}

@Injectable()
export class R2Service {
  private readonly logger = new Logger(R2Service.name);
  private readonly s3Client: S3Client;
  private readonly bucketName: string;
  private readonly publicUrl: string;

  constructor(private readonly configService: ConfigService<EnvConfigType>) {
    const r2Config = this.configService.get('r2', { infer: true });

    if (!r2Config) {
      throw new Error('R2 configuration is missing');
    }

    this.bucketName = r2Config.bucketName;
    this.publicUrl = r2Config.publicUrl;

    this.s3Client = new S3Client({
      region: 'auto', // R2 uses 'auto' as region
      endpoint: r2Config.endpoint,
      credentials: {
        accessKeyId: r2Config.accessKeyId,
        secretAccessKey: r2Config.secretAccessKey,
      },
      forcePathStyle: true, // Required for R2 compatibility
    });

    this.logger.log(`R2Service initialized with bucket: ${this.bucketName}`);
    this.logger.log(`Public URL: ${this.publicUrl}`);
    this.logger.log(`Endpoint: ${r2Config.endpoint}`);
    this.logger.log(`Access Key ID: ${r2Config.accessKeyId}`);
    this.logger.log(`Secret Access Key: ${r2Config.secretAccessKey}`);

    this.logger.log('R2Service initialized successfully');
  }

  /**
   * Generate a presigned URL for uploading files to R2
   * @param metadata File metadata including category and entity info
   * @param expiresIn Expiration time in seconds (default: 15 minutes)
   * @returns Presigned upload URL and file information
   */
  async generatePresignedUploadUrl(
    metadata: FileMetadata,
    expiresIn: number = 900, // 15 minutes
  ): Promise<PresignedUploadResponse> {
    try {
      const fileKey = this.generateFileKey(metadata);

      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: fileKey,
        ContentType: metadata.mimeType,
        // Add metadata for tracking
        Metadata: {
          originalName: metadata.originalName,
          category: metadata.category,
          entityId: metadata.entityId || '',
          uploadedAt: new Date().toISOString(),
        },
        // Security headers
        CacheControl: 'public, max-age=31536000', // 1 year cache
        // ContentDisposition: 'inline', // Temporarily removed to fix signature issue
      });

      const uploadUrl = await getSignedUrl(this.s3Client, command, {
        expiresIn,
      });

      const publicUrl = `${this.publicUrl}/${fileKey}`;

      this.logger.log(`Generated presigned URL for file: ${fileKey}`);

      return {
        uploadUrl,
        fileKey,
        publicUrl,
        expiresIn,
      };
    } catch (error) {
      this.logger.error('Failed to generate presigned upload URL', error);
      throw new Error('Failed to generate upload URL');
    }
  }

  /**
   * Delete a file from R2 storage
   * @param fileKey The file key to delete
   */
  async deleteFile(fileKey: string): Promise<void> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: fileKey,
      });

      await this.s3Client.send(command);
      this.logger.log(`Successfully deleted file: ${fileKey}`);
    } catch (error) {
      this.logger.error(`Failed to delete file: ${fileKey}`, error);
      throw new Error('Failed to delete file');
    }
  }

  /**
   * Generate a structured file key with proper naming convention
   * Format: {category}/{year}/{month}/{entityId}/{timestamp}_{randomId}.{ext}
   */
  private generateFileKey(metadata: FileMetadata): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const timestamp = now.getTime();
    const randomId = crypto.randomBytes(8).toString('hex');

    // Extract file extension from mime type or original name
    const extension = this.getFileExtension(
      metadata.mimeType,
      metadata.originalName,
    );

    // Build path components
    const pathComponents = [metadata.category, year.toString(), month];

    // Add entity ID if provided for better organization
    if (metadata.entityId) {
      pathComponents.push(metadata.entityId);
    }

    const fileName = `${timestamp}_${randomId}${extension}`;
    pathComponents.push(fileName);

    return pathComponents.join('/');
  }

  /**
   * Extract file extension from mime type or filename
   */
  private getFileExtension(mimeType: string, originalName: string): string {
    // Common mime type to extension mapping
    const mimeToExt: Record<string, string> = {
      'image/jpeg': '.jpg',
      'image/jpg': '.jpg',
      'image/png': '.png',
      'image/gif': '.gif',
      'image/webp': '.webp',
      'image/svg+xml': '.svg',
      'application/pdf': '.pdf',
      'text/plain': '.txt',
    };

    // Try to get extension from mime type first
    if (mimeToExt[mimeType]) {
      return mimeToExt[mimeType];
    }

    // Fallback to extracting from original filename
    const lastDotIndex = originalName.lastIndexOf('.');
    if (lastDotIndex !== -1) {
      return originalName.substring(lastDotIndex);
    }

    // Default extension if nothing found
    return '.bin';
  }

  /**
   * Extract file key from public URL
   */
  extractFileKeyFromUrl(publicUrl: string): string | null {
    try {
      const url = new URL(publicUrl);
      // Remove leading slash
      return url.pathname.substring(1);
    } catch {
      return null;
    }
  }

  /**
   * Validate file type for specific categories
   */
  validateFileType(mimeType: string, category: string): boolean {
    const allowedTypes: Record<string, string[]> = {
      banner: [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/webp',
        'image/gif',
      ],
      avatar: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
      document: [
        'application/pdf',
        'text/plain',
        'image/jpeg',
        'image/jpg',
        'image/png',
      ],
    };

    return allowedTypes[category]?.includes(mimeType) || false;
  }

  /**
   * Get file size limits for different categories (in bytes)
   */
  getFileSizeLimit(category: string): number {
    const limits: Record<string, number> = {
      banner: 5 * 1024 * 1024, // 5MB
      avatar: 2 * 1024 * 1024, // 2MB
      document: 10 * 1024 * 1024, // 10MB
    };

    return limits[category] || 1024 * 1024; // Default 1MB
  }
}
