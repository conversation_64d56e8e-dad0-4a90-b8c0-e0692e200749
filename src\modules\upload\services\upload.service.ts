import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { R2Service, FileMetadata } from 'src/third-party/r2/r2.service';
import { GenerateUploadUrlDto, DeleteFileDto } from '../dto/upload-request.dto';
import {
  UploadUrlResponseDto,
  DeleteFileResponseDto,
} from '../dto/upload-response.dto';
import {
  FileValidationResult,
  UPLOAD_LIMITS,
} from '../interfaces/upload.interface';

@Injectable()
export class UploadService {
  private readonly logger = new Logger(UploadService.name);

  constructor(private readonly r2Service: R2Service) {}

  /**
   * Generate presigned URL for file upload
   */
  async generateUploadUrl(
    dto: GenerateUploadUrlDto,
  ): Promise<UploadUrlResponseDto> {
    try {
      // Validate file
      const validationResult = this.validateFile(dto);
      if (!validationResult.isValid) {
        throw new BadRequestException({
          message: 'File validation failed',
          errors: validationResult.errors,
        });
      }

      // Prepare metadata
      const metadata: FileMetadata = {
        originalName: dto.originalName,
        mimeType: dto.mimeType,
        size: dto.size,
        category: dto.category,
        entityId: dto.entityId,
      };

      // Generate presigned URL
      const result = await this.r2Service.generatePresignedUploadUrl(metadata);

      this.logger.log(
        `Generated upload URL for file: ${dto.originalName}, category: ${dto.category}`,
      );

      return {
        uploadUrl: result.uploadUrl,
        fileKey: result.fileKey,
        publicUrl: result.publicUrl,
        expiresIn: result.expiresIn,
        instructions:
          'Use PUT method to upload the file to the uploadUrl with Content-Type header matching the mimeType',
      };
    } catch (error) {
      this.logger.error('Failed to generate upload URL', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to generate upload URL');
    }
  }

  /**
   * Delete file from storage
   */
  async deleteFile(dto: DeleteFileDto): Promise<DeleteFileResponseDto> {
    try {
      let fileKey = dto.fileKey;

      // If it's a full URL, extract the file key
      if (dto.fileKey.startsWith('http')) {
        const extractedKey = this.r2Service.extractFileKeyFromUrl(dto.fileKey);
        if (!extractedKey) {
          throw new BadRequestException('Invalid file URL format');
        }
        fileKey = extractedKey;
      }

      await this.r2Service.deleteFile(fileKey);

      this.logger.log(`Successfully deleted file: ${fileKey}`);

      return {
        message: 'File deleted successfully',
        fileKey,
      };
    } catch (error) {
      this.logger.error(`Failed to delete file: ${dto.fileKey}`, error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new NotFoundException('File not found or could not be deleted');
    }
  }

  /**
   * Validate file based on category rules
   */
  private validateFile(dto: GenerateUploadUrlDto): FileValidationResult {
    const errors: string[] = [];

    // Check if category exists in limits
    const categoryLimits = UPLOAD_LIMITS[dto.category];
    if (!categoryLimits) {
      errors.push(`Invalid file category: ${dto.category}`);
      return { isValid: false, errors };
    }

    // Validate file type
    if (!this.r2Service.validateFileType(dto.mimeType, dto.category)) {
      errors.push(
        `File type ${dto.mimeType} is not allowed for category ${dto.category}. ` +
          `Allowed types: ${categoryLimits.allowedTypes.join(', ')}`,
      );
    }

    // Validate file size
    if (dto.size && dto.size > categoryLimits.maxSize) {
      const maxSizeMB = Math.round(categoryLimits.maxSize / (1024 * 1024));
      errors.push(
        `File size ${Math.round(dto.size / (1024 * 1024))}MB exceeds maximum allowed size of ${maxSizeMB}MB for category ${dto.category}`,
      );
    }

    // Validate filename
    // if (!this.isValidFilename(dto.originalName)) {
    //   errors.push(
    //     'Invalid filename. Only letters, numbers, dots, hyphens, and underscores are allowed',
    //   );
    // }

    // Category-specific validations
    if (dto.category === 'banner' && dto.entityId) {
      // For banner uploads, entity ID should be a valid booking page ID
      if (!this.isValidObjectId(dto.entityId)) {
        errors.push('Invalid entity ID format for banner upload');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate filename format
   */
  private isValidFilename(filename: string): boolean {
    // Allow letters, numbers, dots, hyphens, underscores
    const filenameRegex = /^[a-zA-Z0-9._-]+$/;
    return filenameRegex.test(filename) && filename.length <= 255;
  }

  /**
   * Validate MongoDB ObjectId format
   */
  private isValidObjectId(id: string): boolean {
    const objectIdRegex = /^[0-9a-fA-F]{24}$/;
    return objectIdRegex.test(id);
  }

  /**
   * Get upload configuration for a specific category
   */
  getUploadConfig(category: string) {
    const limits = UPLOAD_LIMITS[category];
    if (!limits) {
      throw new BadRequestException(`Invalid category: ${category}`);
    }

    return {
      category,
      maxFileSize: limits.maxSize,
      maxFileSizeMB: Math.round(limits.maxSize / (1024 * 1024)),
      allowedMimeTypes: limits.allowedTypes,
      allowedExtensions: this.getExtensionsFromMimeTypes(limits.allowedTypes),
    };
  }

  /**
   * Get file extensions from MIME types
   */
  private getExtensionsFromMimeTypes(mimeTypes: string[]): string[] {
    const mimeToExt: Record<string, string> = {
      'image/jpeg': '.jpg',
      'image/jpg': '.jpg',
      'image/png': '.png',
      'image/gif': '.gif',
      'image/webp': '.webp',
      'image/svg+xml': '.svg',
      'application/pdf': '.pdf',
      'text/plain': '.txt',
    };

    return mimeTypes
      .map((mime) => mimeToExt[mime])
      .filter((ext) => ext !== undefined);
  }

  /**
   * Cleanup expired upload URLs (for future cron job implementation)
   */
  async cleanupExpiredUploads(): Promise<void> {
    // This method can be implemented later for cleanup tasks
    // For now, it's a placeholder for future cron job implementation
    this.logger.log(
      'Cleanup expired uploads - placeholder for future implementation',
    );
  }
}
