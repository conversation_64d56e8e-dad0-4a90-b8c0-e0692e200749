# Upload Integration Example

## Frontend Integration (React/Vue/Angular)

### 1. Banner Upload Component

```typescript
// BannerUpload.tsx
import React, { useState } from 'react';

interface BannerUploadProps {
  bookingPageId: string;
  onUploadSuccess: (publicUrl: string) => void;
  onUploadError: (error: string) => void;
}

export const BannerUpload: React.FC<BannerUploadProps> = ({
  bookingPageId,
  onUploadSuccess,
  onUploadError
}) => {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file on client-side
    if (!validateFile(file)) {
      onUploadError('Invalid file type or size');
      return;
    }

    try {
      setUploading(true);
      setProgress(0);

      // 1. Get upload URL from backend
      const uploadUrlResponse = await fetch('/agent/upload/banner', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          originalName: file.name,
          mimeType: file.type,
          size: file.size,
          entityId: bookingPageId
        })
      });

      if (!uploadUrlResponse.ok) {
        throw new Error('Failed to get upload URL');
      }

      const { uploadUrl, publicUrl } = await uploadUrlResponse.json();
      setProgress(25);

      // 2. Upload file to R2 using presigned URL
      const uploadResponse = await fetch(uploadUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': file.type
        },
        body: file
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload file');
      }

      setProgress(100);
      onUploadSuccess(publicUrl);

    } catch (error) {
      onUploadError(error.message);
    } finally {
      setUploading(false);
      setProgress(0);
    }
  };

  const validateFile = (file: File): boolean => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      return false;
    }

    if (file.size > maxSize) {
      return false;
    }

    return true;
  };

  return (
    <div className="banner-upload">
      <input
        type="file"
        accept="image/jpeg,image/png,image/webp,image/gif"
        onChange={handleFileSelect}
        disabled={uploading}
      />
      
      {uploading && (
        <div className="upload-progress">
          <div className="progress-bar" style={{ width: `${progress}%` }} />
          <span>Uploading... {progress}%</span>
        </div>
      )}
    </div>
  );
};
```

### 2. Upload Service (Frontend)

```typescript
// uploadService.ts
export class UploadService {
  private baseUrl = '/agent/upload';
  private authToken: string;

  constructor(authToken: string) {
    this.authToken = authToken;
  }

  async uploadBanner(file: File, entityId: string): Promise<string> {
    // Get upload URL
    const uploadUrlResponse = await this.generateUploadUrl({
      originalName: file.name,
      mimeType: file.type,
      size: file.size,
      category: 'banner',
      entityId
    });

    // Upload file
    await this.uploadFile(uploadUrlResponse.uploadUrl, file);

    return uploadUrlResponse.publicUrl;
  }

  async generateUploadUrl(params: {
    originalName: string;
    mimeType: string;
    size: number;
    category: string;
    entityId: string;
  }) {
    const response = await fetch(`${this.baseUrl}/generate-url`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params)
    });

    if (!response.ok) {
      throw new Error('Failed to generate upload URL');
    }

    return response.json();
  }

  async uploadFile(uploadUrl: string, file: File): Promise<void> {
    const response = await fetch(uploadUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': file.type
      },
      body: file
    });

    if (!response.ok) {
      throw new Error('Failed to upload file');
    }
  }

  async deleteFile(fileKey: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/file`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ fileKey })
    });

    if (!response.ok) {
      throw new Error('Failed to delete file');
    }
  }

  async getUploadConfig(category: string) {
    const response = await fetch(`${this.baseUrl}/config/${category}`, {
      headers: {
        'Authorization': `Bearer ${this.authToken}`
      }
    });

    if (!response.ok) {
      throw new Error('Failed to get upload config');
    }

    return response.json();
  }
}
```

## Backend Integration

### 1. BookingPage Service Integration

```typescript
// booking-page.service.ts
import { UploadService } from '../upload/services/upload.service';

@Injectable()
export class BookingPageService {
  constructor(
    private readonly uploadService: UploadService,
    // ... other dependencies
  ) {}

  async updateBannerImage(
    bookingPageId: string,
    imageData: {
      originalName: string;
      mimeType: string;
      size: number;
    }
  ) {
    // Generate upload URL for banner
    const uploadResult = await this.uploadService.generateUploadUrl({
      ...imageData,
      category: 'banner',
      entityId: bookingPageId,
    });

    // Return upload URL to client
    return uploadResult;
  }

  async deleteBannerImage(bookingPageId: string, imageUrl: string) {
    // Delete old banner image
    await this.uploadService.deleteFile({ fileKey: imageUrl });

    // Update booking page to remove banner
    await this.updateBookingPage(bookingPageId, {
      'blocks.$[elem].data.image': '',
    }, {
      arrayFilters: [{ 'elem.type': 'banner' }]
    });
  }
}
```

### 2. Cleanup Service (Cron Job)

```typescript
// cleanup.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { UploadService } from '../upload/services/upload.service';

@Injectable()
export class CleanupService {
  private readonly logger = new Logger(CleanupService.name);

  constructor(private readonly uploadService: UploadService) {}

  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async cleanupUnusedFiles() {
    this.logger.log('Starting cleanup of unused files...');
    
    try {
      await this.uploadService.cleanupExpiredUploads();
      this.logger.log('Cleanup completed successfully');
    } catch (error) {
      this.logger.error('Cleanup failed', error);
    }
  }
}
```

## Error Handling Examples

### Frontend Error Handling

```typescript
const handleUploadError = (error: any) => {
  if (error.status === 400) {
    // Validation error
    const errorData = error.json();
    if (errorData.errors) {
      // Show specific validation errors
      errorData.errors.forEach(err => {
        showToast(err, 'error');
      });
    }
  } else if (error.status === 413) {
    // File too large
    showToast('File is too large. Maximum size is 5MB for banners.', 'error');
  } else if (error.status === 415) {
    // Unsupported media type
    showToast('File type not supported. Please use JPG, PNG, or WebP.', 'error');
  } else {
    // Generic error
    showToast('Upload failed. Please try again.', 'error');
  }
};
```

### Backend Error Handling

```typescript
@Post('upload-banner')
async uploadBanner(@Body() dto: any) {
  try {
    return await this.uploadService.generateUploadUrl(dto);
  } catch (error) {
    if (error instanceof BadRequestException) {
      throw error; // Re-throw validation errors
    }
    
    this.logger.error('Upload failed', error);
    throw new InternalServerErrorException('Upload service unavailable');
  }
}
```

## Testing Examples

### Frontend Testing

```typescript
// uploadService.test.ts
describe('UploadService', () => {
  it('should upload banner successfully', async () => {
    const mockFile = new File(['test'], 'banner.jpg', { type: 'image/jpeg' });
    const service = new UploadService('test-token');
    
    // Mock fetch responses
    global.fetch = jest.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          uploadUrl: 'https://test-upload-url.com',
          publicUrl: 'https://test-public-url.com'
        })
      })
      .mockResolvedValueOnce({ ok: true });

    const result = await service.uploadBanner(mockFile, 'entity-id');
    
    expect(result).toBe('https://test-public-url.com');
  });
});
```

### Integration Testing

```typescript
// upload.e2e-spec.ts
describe('Upload (e2e)', () => {
  it('/agent/upload/banner (POST)', () => {
    return request(app.getHttpServer())
      .post('/agent/upload/banner')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        originalName: 'test-banner.jpg',
        mimeType: 'image/jpeg',
        size: 1024000,
        entityId: '507f1f77bcf86cd799439011'
      })
      .expect(200)
      .expect((res) => {
        expect(res.body.uploadUrl).toBeDefined();
        expect(res.body.publicUrl).toBeDefined();
        expect(res.body.fileKey).toMatch(/^banner\/\d{4}\/\d{2}\//);
      });
  });
});
```
