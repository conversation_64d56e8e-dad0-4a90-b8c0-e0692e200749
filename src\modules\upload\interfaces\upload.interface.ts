export interface FileValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface UploadConfig {
  maxFileSize: number;
  allowedMimeTypes: string[];
  allowedExtensions: string[];
}

export interface FileUploadMetadata {
  originalName: string;
  mimeType: string;
  size: number;
  category: 'banner' | 'avatar' | 'document';
  entityId?: string;
  uploadedBy?: string;
  uploadedAt: Date;
}

export interface StorageFile {
  fileKey: string;
  publicUrl: string;
  metadata: FileUploadMetadata;
}

export interface UploadLimits {
  banner: {
    maxSize: number; // 5MB
    allowedTypes: string[];
  };
  avatar: {
    maxSize: number; // 2MB
    allowedTypes: string[];
  };
  document: {
    maxSize: number; // 10MB
    allowedTypes: string[];
  };
}

export const UPLOAD_LIMITS: UploadLimits = {
  banner: {
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/webp',
      'image/gif',
    ],
  },
  avatar: {
    maxSize: 2 * 1024 * 1024, // 2MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  },
  document: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: [
      'application/pdf',
      'text/plain',
      'image/jpeg',
      'image/jpg',
      'image/png',
    ],
  },
};

export interface UploadValidationError {
  field: string;
  message: string;
  code: string;
}

export enum UploadErrorCode {
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  INVALID_FILENAME = 'INVALID_FILENAME',
  MISSING_ENTITY_ID = 'MISSING_ENTITY_ID',
  UPLOAD_FAILED = 'UPLOAD_FAILED',
  DELETE_FAILED = 'DELETE_FAILED',
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
}
