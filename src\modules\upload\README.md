# Upload Module

Module này cung cấp functionality để upload files lên Cloudflare R2 storage sử dụng presigned URLs.

## Features

- ✅ Presigned URL generation cho secure uploads
- ✅ File validation (type, size, filename)
- ✅ Structured file organization với proper naming convention
- ✅ Support multiple file categories (banner, avatar, document)
- ✅ File deletion functionality
- ✅ Comprehensive error handling
- ✅ Best practices cho security và performance

## Architecture

```
src/modules/upload/
├── dto/
│   ├── upload-request.dto.ts    # Request DTOs
│   └── upload-response.dto.ts   # Response DTOs
├── interfaces/
│   └── upload.interface.ts      # Interfaces và constants
├── services/
│   ├── upload.service.ts        # Main upload service
│   └── upload.service.spec.ts   # Unit tests
├── upload.module.ts             # Module definition
└── README.md                    # Documentation
```

## Configuration

### Environment Variables

Thêm vào `.env` file:

```env
# R2 Storage (Cloudflare R2)
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_ACCESS_KEY_ID=your-r2-access-key-id
R2_SECRET_ACCESS_KEY=your-r2-secret-access-key
R2_BUCKET_NAME=booking-easy-uploads
R2_PUBLIC_URL=https://your-custom-domain.com
```

### File Organization Structure

Files được organize theo structure sau:

```
{category}/{year}/{month}/{entityId}/{timestamp}_{randomId}.{ext}

Examples:
- banner/2024/01/507f1f77bcf86cd799439011/1704067200000_a1b2c3d4.jpg
- avatar/2024/01/user123/1704067200000_b2c3d4e5.png
- document/2024/01/booking456/1704067200000_c3d4e5f6.pdf
```

## API Endpoints

### 1. Generate Upload URL

```http
POST /agent/upload/generate-url
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "originalName": "banner-image.jpg",
  "mimeType": "image/jpeg",
  "category": "banner",
  "size": 1024000,
  "entityId": "507f1f77bcf86cd799439011"
}
```

Response:
```json
{
  "uploadUrl": "https://presigned-url...",
  "fileKey": "banner/2024/01/507f1f77bcf86cd799439011/1704067200000_a1b2c3d4.jpg",
  "publicUrl": "https://your-domain.com/banner/2024/01/507f1f77bcf86cd799439011/1704067200000_a1b2c3d4.jpg",
  "expiresIn": 900,
  "instructions": "Use PUT method to upload the file to the uploadUrl"
}
```

### 2. Upload File (Client-side)

```javascript
// Client sử dụng uploadUrl để upload file
const response = await fetch(uploadUrl, {
  method: 'PUT',
  headers: {
    'Content-Type': 'image/jpeg'
  },
  body: fileBlob
});
```

### 3. Delete File

```http
DELETE /agent/upload/file
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "fileKey": "banner/2024/01/507f1f77bcf86cd799439011/1704067200000_a1b2c3d4.jpg"
}
```

### 4. Get Upload Config

```http
GET /agent/upload/config/banner
Authorization: Bearer {jwt_token}
```

Response:
```json
{
  "category": "banner",
  "maxFileSize": 5242880,
  "maxFileSizeMB": 5,
  "allowedMimeTypes": ["image/jpeg", "image/png", "image/webp"],
  "allowedExtensions": [".jpg", ".png", ".webp"]
}
```

### 5. Banner Upload Shortcut

```http
POST /agent/upload/banner
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "originalName": "banner.jpg",
  "mimeType": "image/jpeg",
  "size": 1024000,
  "entityId": "507f1f77bcf86cd799439011"
}
```

## File Categories & Limits

| Category | Max Size | Allowed Types |
|----------|----------|---------------|
| banner   | 5MB      | image/jpeg, image/png, image/webp, image/gif |
| avatar   | 2MB      | image/jpeg, image/png, image/webp |
| document | 10MB     | application/pdf, text/plain, image/jpeg, image/png |

## Usage Examples

### Frontend Integration

```typescript
// 1. Get upload URL
const uploadResponse = await fetch('/agent/upload/banner', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    originalName: file.name,
    mimeType: file.type,
    size: file.size,
    entityId: bookingPageId
  })
});

const { uploadUrl, publicUrl } = await uploadResponse.json();

// 2. Upload file to R2
await fetch(uploadUrl, {
  method: 'PUT',
  headers: {
    'Content-Type': file.type
  },
  body: file
});

// 3. Use publicUrl in your application
console.log('File uploaded successfully:', publicUrl);
```

### Backend Integration

```typescript
// Inject UploadService
constructor(private readonly uploadService: UploadService) {}

// Generate upload URL
const result = await this.uploadService.generateUploadUrl({
  originalName: 'banner.jpg',
  mimeType: 'image/jpeg',
  category: 'banner',
  entityId: bookingPageId
});

// Delete file
await this.uploadService.deleteFile({
  fileKey: 'banner/2024/01/test.jpg'
});
```

## Security Features

- ✅ JWT authentication required
- ✅ File type validation
- ✅ File size limits
- ✅ Filename sanitization
- ✅ Presigned URLs với expiration (15 minutes)
- ✅ Entity ID validation cho proper access control

## Best Practices

1. **Always validate files on client-side** trước khi request upload URL
2. **Handle upload errors gracefully** với proper user feedback
3. **Store publicUrl** trong database thay vì fileKey
4. **Implement cleanup logic** để xóa unused files
5. **Use appropriate file categories** để maintain organization
6. **Monitor file usage** và implement quota limits nếu cần

## Testing

Run tests:
```bash
npm test upload
```

Tests cover:
- ✅ Upload URL generation
- ✅ File validation logic
- ✅ File deletion
- ✅ Error handling
- ✅ Configuration retrieval
